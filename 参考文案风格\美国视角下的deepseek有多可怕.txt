老铁们，你们可能还真不知道dpc这一刀对美国到底有多致命。

那美国现在是好有一比呀，他是花大钱上泰国去给我找个人妖主打一个，

难上加难呐！有老铁说，那不就是个ai软件嘛，

这个ai软件可不一样，他可以说直接动了美国的国本。为什么这么说呢？

你看近十几年来，美国不惜一切代价、不择一切手段，

想要推进的这个国家甚至是整个世界的发展方向，

就是ai人工智能。因为这是美国自认为已经为数不多了，

整条产业链都可以领先全世界的这么一条科技发展道路。如果他真能把这一整条产业链上的各种技术链条，

从算力到算法，重新把技术把权抓在手里，

然后再有一段时间给他融入到全世界的工业生产体系里面，

那么美国这个霸主地位就还能延续一段时间。你看现在全世界人都知道美国现在最大的问题是什么，

制造业空心化，但是这种情况在以前那可不叫空心化呀，

这恰恰就是美国精心设计、努力多年追求的一个结果，

这叫美国主导的全球一体化，

这叫全新的美式全球殖民体系。它追求的就是我不再干工厂了，

我不再生产产品了，我要的是要把我的技术和钱放到全世界去，

那么全世界的工厂就都是我的。就像以前那个苹果，

最多的时候，全世界有百分之九十以上苹果都是咱造了，但是那个叫空心化吗？

那个不叫，因为大多数的利润也被他们拿走了。台积电、三星、阿斯麦尔。

那叫空心化吗？那不叫。你的技术、你的钱全都由我控制，

你生产的产品你卖给谁，我说了也算，这不叫空心化。

真正的空心化是一个华为、两个华为、三个华为，越来越多的华为出现了，

我开始自个大网络，自个造芯片，我挣多少钱和你没关系，

我造东西造什么样的，卖给谁，你也说了不算。这个才叫空心化。

是因为他以前那些技术，一点一点，一点一点逐步被突破了，他已经控制不了全世界那么多工厂了，

才导致今天他所谓的的空心化。所以现在美国的当务之急就是研究一种全新的、由他掌握的技术，

再次融到全世界的工业体系里边去，

重新掌握全世界的工厂，彻底解决的空心化的问题。

那些所谓的工业回流，那都是喊给美国老百姓听了，

忽悠选票用的，他也不可能真把工厂都搬回去，他自个重新开始干粗活累活。

这种情况是他彻底失败了，从霸主位置上退下来之后才会去干的东西。

所以你看他近十年，从打压华为开始，不遗余力，不择手段，

不惜代价。因为在他们的规划里边，最上面的是各种ai人工智能的应用，

下面呢，是海量的算力来支持应用的涌现，再往下就是芯片制造业来提供海量的算力。

你琢磨他拼老命赌上所有身价，

给全世界画了这么一张发展蓝图，他就不可能让任何人跟他分一杯羹。

尤其是咱们。你看那个英伟达，一个卖显卡的，

他凭什么十年前市值九十亿美元，现在最高干到三万多亿啊，

就是美。国的资本在这条路上疯狂的梭哈，就在这条链上，十年前开始卡华为，

一直到现在，所有芯片相关产业全部卡死，

但是就卡成这样，他们竟然发现仍然没卡住。

有老铁就问了他们，十年前为什么不像现在这种彻底卡死？兄弟们，这种卡法是什么好卡法吗？

这种卡法那是伤敌一千，

自损一千二，但凡是有任何其他办法，他都不带走这条路的。

这就好比葵花宝典卡出来个东方不败，辟邪剑谱卡出来的华山派，

那垮脸牌，刀片卡出来两堆药材，自古以来这么卡，卡出来的那都是什么玩意啊！

哎，现在美国也卡这了。最让他们崩溃的是，

他们付出了这么大代价，结果突然从一个根本没听说过名的小门派里面蹦出一个无名小卒，

和他们打的不分上下。这就证明他们选的功法就错了，

近十年来付出的巨大努力，方向也错了，

最主要我们回头路断了。悲凉的歌声不禁从耳畔响起。再回

回首已这段归途，

再回首，荆棘密布，

他这个时候想要再掉头太难了。

这么多年来，他对自己发展ai的这条技术逻辑链深信不疑，

非常自信而且坚定贯彻。他们就认为对芯片、对算力就是最简单最有效的办法，

既然又简单又有效，谁还费那么大劲去研究算法啊？

而且他们对自己在人工智能的研究方向和技术优势方面太自信了，

于是乎就布局了这么长时间。之后，二零二五年一开年，

他们直接就开启了妄图毕其功于一亿的终极大招星际之门计划。

要花五千亿美元打造一个人类历史上最大的人工智能基础设施项目。

他们的目标就是用这五千亿裹挟全世界的资本，

重新在亿富和捆绑在美国这条科技脊梁骨上。

英伟达再出七十万片高端芯片，

在美国建二十所超算中心，以这个星球上无与伦比的算力，

支持他们构想的这个ai人工智能一飞冲天。而且这个事在他们看来已经是水到渠成，

大势所趋，天下已定，胜负一分。

这个玉虚公的天元鼎已经就位，那个顶盖已经盖上了，他们的发财树已经在顶盖上生根发芽啊！

这个世界将会再次为他们源源不断的提供弹药。

正在这个时候，deep siek横空出世，不但顶盖给掀了，

整个顶都给他砸了。这么多年来，他们就认为是高端芯片提供强大的算力来支持应用的功能，

这条店铺不破的逻辑链崩了。

其实客观来讲，deep sea的功能并不比他们强大多少，和他们差不多，

但是就实现和你差不多的功能，根本就不需要那么强大的算力，

那么多高端芯片，英伟达七十万片高端芯片，那就是个笑话，

那刚刚还牛逼哄哄的mvd啊，一下就变成了vigo啊，

那以前谁用的多谁牛掰，现在就变成了谁用的多那么谁拉胯。

他现在的处境就跟我刚才说那个英文单词一样，

以前一个英文单词谁认识，证明他妈只是储备行了，现在谁认识这个单词，

证明你的储备就剩单词了，你算法不行，所以你只能拿算力来凑，

你一味的追。求时长，证明你技术不够，法力不透。

所以你看他那个股价，那真是搂一搂隔壁王叔叔，那叫意想不到的暴跌呀，

一天干下去百分之十七。而且你们看了吧，就我刚才说那个所谓的美国科技脊梁骨american technology，

羊蝎了，那上面那些公司有一个算一个，

都是大黄巴豆咕噜噜噗，

我离死不远了。尤其是那个美国三代单传的独苗，最后一根救命稻草open ai，

那个名起的跟魏宗贤一样，既不忠又不贤，

他是一点都不open，他的软件完全是必然的，它应该叫close ai。

其实这也可以理解吧，美国布局了这么多年，费这么大劲研究了这是一项承载美国国之大运的技术，

肯定他捂得死死的，一丁点都不能泄露出去，

结果dpc上来就给他干了一件让他头皮都发麻的事，

直接开源。有很多老铁他不明白什么叫开源，他觉得开源那不就人就照抄就完了吗？

你们机密全泄给人家了。不是这么回事，所谓开源，

虽然开放了大部分原代码，但是真正的核心算法和这个语句，

他是不开放的。就像有时候咱出去吃饭，你看那个那个开放式厨房，那个那个厨子都当你面啪啪切字，

切完了之后起锅烧油，菜一扔，三勺盐两勺糖，

啪啪一点吧，出锅了，但是做菜的过程中，他真从胯兜摸出个小瓶，

完了之后炒菜的啪啪撒两下，你就再开放式厨房，

你再开远，这里面装的啥你也知不道，甚至我可以把菜谱都给你拿，

你拿了我菜谱自个出去开连锁店。都行。而且这个菜单你拿到手之后，

你店开在哪？你可以根据当地的口味，你再调整一下自个菜单。你开在四川，

开在湖南你就往里放点辣椒，你开在山西，你往里淋着点老陈醋，

你开在韩国，你往里摆两片西瓜，你开在日本，你往里撒把****这个你自个定。

一旦开圆了，就会迅速大面积的占领市场。美国自以为是独占鳌头，独霸全球的这么一个科技制高点，

瞬间被强行拉回平均线了。

再按美国训练大模型这个成本来看，他哪怕现在就是b元，

大把大把的收钱，现在open i还天天赔钱呢，你这如果一旦开元，

你让他怎么活呀？不过他要是胆敢继续保持b元的话，

前车之鉴就在那。那死去多年的诺基亚布楞一下就坐起来了，

发出不甘的怒吼塞班！塞班！但凡上点岁数的老铁，

你们应该都记了，当年诺基亚多凶啊，施展率干到百分之四十啊，

那真是老矮仰壳晒太阳，那叫一个如日中天啊。而且人家早早就开始布局智能机了，

一个塞班系统就把小院高墙给垒起来了，

结果呢，让安卓系统一个开源，把个商业巨无霸瞬间打的土崩瓦解，

灰飞烟灭。所以现在这个open ai就难受死了，

你如果也跟了开源的话，就你这个训练成本，你一个偷看花多少钱，

那你不得赔死啊？但是你如果继续避援呢？你的下场只有一个

塞巴。所以说open ai现在的处境，就好像西门庆偷腥偷到一半，

突然发现武大郎和潘金莲来了个灵魂。互换他整了一个，

进退他两难呐。最主要的，现在人工智能也只是刚刚起步，

而且咱客观来讲啊，这个d f c可在某些方面肯定有他的优势，

但是你要从总体功能上来看，他并没有超越美国，他只是跟上了节奏，

跟上了步伐，没掉队而已。你从芯片制造和算力支持方面去看，

的确还有很大差距，但是这些差距正在以肉眼可见的速度一点一点的在追平。

就像二零二三年以前，

咱连十四纳米芯片都量产不了，谁能想到转过年的二十四年，

华为九零二零麒麟芯片七纳米的已经用上了，近年七纳米的芯片就能大规模量产。

你这么看，再过两年呢？再过五年呢？

所以美国最大的问题就是他从建国到现在都不到一个二百五，

他都没经过一个轮回，那小年轻他不是过来人。所以说挣扎挣扎有情可原。

你就包括我现在看刚结婚时候自己也是一样，

老是觉得怎么赶车的能让驴说了算吗？这这推车的老汉还能让车拽着走？

但是你现在回头看看就明白了，上司看来优势在你，

但是趋势已经不在。